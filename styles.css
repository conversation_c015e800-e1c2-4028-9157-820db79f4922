/* <PERSON>'s Web Design - Main Stylesheet */
/* Color Palette: <PERSON> Blue (#0D1B2A), <PERSON><PERSON> (#4A5568), <PERSON> (#F7FAFC), <PERSON> Blue (#3B82F6) */

@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600&display=swap");

/* CSS Variables for consistent theming */
:root {
  --primary-dark: #0d1b2a;
  --secondary-gray: #4a5568;
  --soft-white: #f7fafc;
  --accent-blue: #3b82f6;
  --pure-white: #ffffff;
  --text-light: #718096;
  --border-light: #e2e8f0;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--secondary-gray) 100%
  );
  --gradient-accent: linear-gradient(
    135deg,
    var(--accent-blue) 0%,
    #2563eb 100%
  );
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: var(--secondary-gray);
  background-color: var(--soft-white);
  overflow-x: hidden;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  line-height: 1.2;
}

h1 {
  font-size: 3.5rem;
  font-weight: 800;
}
h2 {
  font-size: 2.5rem;
  font-weight: 700;
}
h3 {
  font-size: 2rem;
  font-weight: 600;
}
h4 {
  font-size: 1.5rem;
  font-weight: 600;
}
h5 {
  font-size: 1.25rem;
  font-weight: 500;
}
h6 {
  font-size: 1rem;
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
  color: var(--text-light);
  font-weight: 400;
}

a {
  text-decoration: none;
  color: var(--accent-blue);
  transition: all 0.3s ease;
}

a:hover {
  color: #2563eb;
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  margin-bottom: 1rem;
}

.section-title p {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Header and Navigation */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(13, 27, 42, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header.scrolled {
  background: rgba(13, 27, 42, 0.98);
  box-shadow: 0 2px 20px var(--shadow-medium);
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.logo {
  font-family: "Poppins", sans-serif;
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--pure-white);
}

.logo span {
  color: var(--accent-blue);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--pure-white);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background: var(--accent-blue);
  color: var(--pure-white);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--pure-white);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: var(--gradient-accent);
  color: var(--pure-white);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--accent-blue);
  border: 2px solid var(--accent-blue);
}

.btn-secondary:hover {
  background: var(--accent-blue);
  color: var(--pure-white);
}

.btn-outline {
  background: transparent;
  color: var(--pure-white);
  border: 2px solid var(--pure-white);
}

.btn-outline:hover {
  background: var(--pure-white);
  color: var(--primary-dark);
}

/* Cards */
.card {
  background: var(--pure-white);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px var(--shadow-medium);
}

/* Grid System */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Utilities */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 2rem;
}

.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 2rem;
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  h1 {
    font-size: 2.5rem;
  }
  h2 {
    font-size: 2rem;
  }
  h3 {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
  .section {
    padding: 3rem 0;
  }

  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--primary-dark);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
  }

  .nav-menu.active {
    left: 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
  .btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
  .card {
    padding: 1.5rem;
  }
}

/* Hero Section */
.hero {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  text-align: center;
  color: var(--pure-white);
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  color: var(--pure-white);
  font-size: 4rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Service Cards */
.service-icon {
  color: var(--accent-blue);
  margin-bottom: 1.5rem;
  text-align: center;
}

.service-icon svg {
  width: 60px;
  height: 60px;
}

.service-link {
  display: inline-block;
  margin-top: 1rem;
  color: var(--accent-blue);
  font-weight: 600;
  transition: all 0.3s ease;
}

.service-link:hover {
  transform: translateX(5px);
}

/* Background Gradient Section */
.bg-gradient {
  background: var(--gradient-primary);
  color: var(--pure-white);
}

/* Feature List */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  background: var(--accent-blue);
  color: var(--pure-white);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--pure-white);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Testimonials */
.testimonial-card {
  text-align: center;
}

.stars {
  color: #ffd700;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.testimonial-content p {
  font-style: italic;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.testimonial-author strong {
  color: var(--primary-dark);
  display: block;
  margin-bottom: 0.25rem;
}

.testimonial-author span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  background: var(--soft-white);
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.cta-content h2 {
  margin-bottom: 1rem;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

/* Footer */
.footer {
  background: var(--primary-dark);
  color: var(--pure-white);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  color: var(--pure-white);
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: var(--accent-blue);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-links a {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.social-links a:hover {
  transform: scale(1.2);
}

.contact-info p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Page Hero */
.page-hero {
  background: var(--gradient-primary);
  padding: 8rem 0 4rem;
  text-align: center;
  color: var(--pure-white);
}

.page-hero-content h1 {
  color: var(--pure-white);
  font-size: 3rem;
  margin-bottom: 1rem;
}

.page-hero-content p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
}

/* Service Detail Sections */
.service-detail {
  padding: 5rem 0;
}

.service-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.service-content.reverse {
  direction: rtl;
}

.service-content.reverse > * {
  direction: ltr;
}

.service-icon-large {
  color: var(--accent-blue);
  margin-bottom: 2rem;
}

.service-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: var(--text-light);
}

.service-features h3,
.service-process h3,
.service-benefits h3 {
  color: var(--primary-dark);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.service-features ul {
  list-style: none;
  padding: 0;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--text-light);
  font-weight: 500;
}

/* Process Steps */
.process-steps {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-number {
  background: var(--accent-blue);
  color: var(--pure-white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
}

.step-content p {
  color: var(--text-light);
  margin: 0;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.benefit-item {
  padding: 1rem;
  background: var(--pure-white);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.benefit-item h4 {
  color: var(--primary-dark);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.benefit-item p {
  color: var(--text-light);
  font-size: 0.9rem;
  margin: 0;
}

/* SEO Metrics */
.seo-metrics {
  margin-top: 2rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1.5rem;
  background: var(--soft-white);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.metric-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
}

.metric-label {
  color: var(--text-light);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Integration Types */
.integration-types {
  margin-top: 2rem;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.integration-item {
  padding: 1rem;
  background: var(--accent-blue);
  color: var(--pure-white);
  text-align: center;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Background Light */
.bg-light {
  background: var(--soft-white);
}

/* Service Mockups */
.service-mockup {
  max-width: 400px;
  margin: 0 auto;
}

/* Browser Mockup */
.mockup-browser {
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 8px 30px var(--shadow-medium);
  overflow: hidden;
}

.browser-header {
  background: #f1f3f4;
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.browser-buttons {
  display: flex;
  gap: 0.5rem;
}

.browser-buttons span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f57;
}

.browser-buttons span:nth-child(2) {
  background: #ffbd2e;
}

.browser-buttons span:nth-child(3) {
  background: #28ca42;
}

.browser-content {
  padding: 2rem;
}

.mockup-website {
  background: var(--soft-white);
  border-radius: 8px;
  padding: 1.5rem;
}

.mockup-header {
  height: 40px;
  background: var(--accent-blue);
  border-radius: 4px;
  margin-bottom: 1rem;
}

.mockup-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mockup-text-line {
  height: 12px;
  background: var(--border-light);
  border-radius: 2px;
}

.mockup-text-line.short {
  width: 60%;
}

/* UX Mockup */
.ux-mockup {
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 8px 30px var(--shadow-medium);
  padding: 2rem;
}

.ux-wireframe {
  background: var(--soft-white);
  border-radius: 8px;
  padding: 1rem;
}

.wireframe-header {
  height: 30px;
  background: var(--border-light);
  border-radius: 4px;
  margin-bottom: 1rem;
}

.wireframe-content {
  display: flex;
  gap: 1rem;
}

.wireframe-sidebar {
  width: 80px;
  height: 120px;
  background: var(--border-light);
  border-radius: 4px;
}

.wireframe-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.wireframe-card {
  height: 35px;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.7;
}

/* SEO Mockup */
.seo-mockup {
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 8px 30px var(--shadow-medium);
  padding: 2rem;
}

.seo-chart {
  background: var(--soft-white);
  border-radius: 8px;
  padding: 1.5rem;
}

.chart-header {
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  text-align: center;
}

.chart-content {
  height: 120px;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-bars {
  display: flex;
  gap: 0.5rem;
  align-items: end;
  height: 100px;
}

.chart-bar {
  width: 20px;
  background: var(--accent-blue);
  border-radius: 2px 2px 0 0;
  animation: growUp 1s ease-out;
}

@keyframes growUp {
  from {
    height: 0;
  }
  to {
    height: var(--final-height, 100%);
  }
}

/* Backend Mockup */
.backend-mockup {
  background: #1e1e1e;
  border-radius: 12px;
  box-shadow: 0 8px 30px var(--shadow-medium);
  overflow: hidden;
}

.code-editor {
  color: #d4d4d4;
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
}

.editor-header {
  background: #2d2d30;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #3e3e42;
}

.editor-tabs {
  display: flex;
  gap: 1rem;
}

.tab {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.tab.active {
  background: #007acc;
  color: white;
}

.editor-content {
  padding: 1.5rem;
  line-height: 1.6;
}

.code-line {
  margin-bottom: 0.25rem;
}

.keyword {
  color: #569cd6;
}
.function {
  color: #dcdcaa;
}
.string {
  color: #ce9178;
}

/* Pricing Section */
.pricing-section {
  background: var(--soft-white);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.pricing-card {
  position: relative;
  text-align: center;
  padding: 2.5rem 2rem;
  background: var(--pure-white);
  border: 2px solid var(--border-light);
  transition: all 0.3s ease;
}

.pricing-card:hover {
  border-color: var(--accent-blue);
  transform: translateY(-5px);
}

.pricing-card.featured {
  border-color: var(--accent-blue);
  transform: scale(1.05);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-blue);
  color: var(--pure-white);
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.pricing-header h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
}

.pricing-header p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.pricing-features {
  margin-bottom: 2rem;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.pricing-features li {
  padding: 0.75rem 0;
  color: var(--text-light);
  border-bottom: 1px solid var(--border-light);
  font-weight: 500;
}

.pricing-features li:last-child {
  border-bottom: none;
}

/* Responsive Design for Services */
@media (max-width: 768px) {
  .page-hero-content h1 {
    font-size: 2.5rem;
  }

  .service-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .service-content.reverse {
    direction: ltr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .integration-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .pricing-card.featured:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 480px) {
  .page-hero {
    padding: 6rem 0 3rem;
  }

  .service-detail {
    padding: 3rem 0;
  }

  .service-mockup {
    max-width: 300px;
  }

  .integration-grid {
    grid-template-columns: 1fr;
  }
}

/* About Page Styles */
.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 3rem;
}

.about-text .lead {
  font-size: 1.3rem;
  font-weight: 500;
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
}

.about-text p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

/* About Visual */
.about-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.visual-element {
  position: relative;
  width: 200px;
  height: 200px;
}

.element-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 120px;
  background: var(--accent-blue);
  border-radius: 50%;
  opacity: 0.8;
}

.element-square {
  position: absolute;
  top: 40px;
  right: 0;
  width: 80px;
  height: 80px;
  background: var(--secondary-gray);
  border-radius: 12px;
  opacity: 0.7;
}

.element-triangle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 60px solid var(--accent-blue);
  opacity: 0.6;
}

/* Values Grid */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.value-card {
  text-align: center;
  padding: 2.5rem 2rem;
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.value-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.value-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.team-member {
  padding: 2rem;
}

.member-photo {
  text-align: center;
  margin-bottom: 1.5rem;
}

.photo-placeholder {
  width: 120px;
  height: 120px;
  background: var(--accent-blue);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--pure-white);
  font-size: 2rem;
  font-weight: bold;
}

.member-info h3 {
  text-align: center;
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
}

.member-role {
  text-align: center;
  color: var(--accent-blue);
  font-weight: 600;
  margin-bottom: 1rem;
}

.member-bio {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.skill-tag {
  background: var(--soft-white);
  color: var(--primary-dark);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid var(--border-light);
}

/* Process Timeline */
.process-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.process-timeline::before {
  content: "";
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.timeline-item {
  position: relative;
  padding-left: 80px;
  margin-bottom: 3rem;
}

.timeline-marker {
  position: absolute;
  left: 0;
  top: 0;
  width: 60px;
  height: 60px;
  background: var(--accent-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--pure-white);
  font-size: 1.5rem;
  font-weight: bold;
  border: 4px solid rgba(255, 255, 255, 0.2);
}

.timeline-content h3 {
  color: var(--pure-white);
  margin-bottom: 1rem;
}

.timeline-content p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
}

.timeline-content ul {
  list-style: none;
  padding: 0;
}

.timeline-content li {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.timeline-content li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--accent-blue);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-card {
  text-align: center;
  padding: 2.5rem 2rem;
  background: var(--pure-white);
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--shadow-light);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px var(--shadow-medium);
}

.stat-card .stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1rem;
}

.stat-card p {
  color: var(--text-light);
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .process-timeline::before {
    left: 15px;
  }

  .timeline-item {
    padding-left: 60px;
  }

  .timeline-marker {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .visual-element {
    width: 150px;
    height: 150px;
  }

  .element-circle {
    width: 90px;
    height: 90px;
  }

  .element-square {
    width: 60px;
    height: 60px;
    top: 30px;
  }

  .element-triangle {
    border-left-width: 30px;
    border-right-width: 30px;
    border-bottom-width: 45px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-marker {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

/* Portfolio Page Styles */
.portfolio-filter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.portfolio-filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--border-light);
  background: var(--pure-white);
  color: var(--secondary-gray);
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.portfolio-filter-btn:hover,
.portfolio-filter-btn.active {
  background: var(--accent-blue);
  color: var(--pure-white);
  border-color: var(--accent-blue);
}

/* Portfolio Grid */
.portfolio-section {
  padding-top: 2rem;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
}

.portfolio-item {
  background: var(--pure-white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: all 0.3s ease;
  opacity: 1;
  transform: scale(1);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

/* Portfolio Image */
.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.project-mockup {
  width: 100%;
  height: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.mockup-screen {
  width: 100%;
  height: 100%;
  background: var(--pure-white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.mockup-header {
  height: 30px;
  background: var(--border-light);
  position: relative;
}

.mockup-header::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff5f57;
  box-shadow: 12px 0 0 #ffbd2e, 24px 0 0 #28ca42;
}

.mockup-content {
  padding: 1rem;
  height: calc(100% - 30px);
}

/* Different mockup styles */
.ecommerce-header {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.business-header {
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}
.startup-header {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}
.nonprofit-header {
  background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}
.restaurant-header {
  background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
}
.tech-header {
  background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
}

/* Product Grid for E-commerce */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  height: 100%;
}

.product-item {
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Business Layout */
.business-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hero-section {
  height: 40%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.8;
}

.services-section {
  display: flex;
  gap: 0.5rem;
  height: 60%;
}

.service-card {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Startup Layout */
.startup-layout {
  height: 100%;
  display: flex;
  gap: 0.5rem;
}

.app-showcase {
  width: 60%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.8;
}

.feature-list {
  width: 40%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Non-profit Layout */
.nonprofit-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cause-banner {
  height: 50%;
  background: linear-gradient(45deg, #43e97b, #38f9d7);
  border-radius: 4px;
  opacity: 0.8;
}

.donation-section {
  height: 50%;
  display: flex;
  gap: 0.5rem;
}

.donation-card {
  width: 70%;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

.progress-bar {
  width: 30%;
  background: var(--accent-blue);
  border-radius: 4px;
  opacity: 0.6;
}

/* Restaurant Layout */
.restaurant-layout {
  height: 100%;
  display: flex;
  gap: 0.5rem;
}

.menu-showcase {
  width: 65%;
  background: linear-gradient(45deg, #fa709a, #fee140);
  border-radius: 4px;
  opacity: 0.8;
}

.reservation-form {
  width: 35%;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Tech Layout */
.tech-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-showcase {
  height: 60%;
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  border-radius: 4px;
  opacity: 0.8;
}

.tech-specs {
  height: 40%;
  display: flex;
  gap: 0.5rem;
}

.spec-item {
  flex: 1;
  background: var(--soft-white);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Portfolio Overlay */
.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 27, 42, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: var(--pure-white);
  padding: 2rem;
}

.overlay-content h3 {
  color: var(--pure-white);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.overlay-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.project-tags span {
  background: var(--accent-blue);
  color: var(--pure-white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Portfolio Info */
.portfolio-info {
  padding: 2rem;
}

.portfolio-info h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.portfolio-info p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Project Results */
.project-results {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.result-item {
  text-align: center;
}

.result-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--accent-blue);
  margin-bottom: 0.25rem;
}

.result-label {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
}

/* Process Steps Grid */
.process-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.process-step-card {
  text-align: center;
  padding: 2.5rem 2rem;
}

.step-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.process-step-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.process-step-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Testimonials Slider */
.testimonials-slider {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.testimonial-slide {
  padding: 2rem;
}

.testimonial-slide .stars {
  color: #ffd700;
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.testimonial-slide blockquote {
  font-size: 1.3rem;
  font-style: italic;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
  quotes: "" " " "" "'" "'";
}

.testimonial-slide blockquote::before {
  content: open-quote;
  font-size: 2rem;
  color: var(--accent-blue);
}

.testimonial-slide blockquote::after {
  content: close-quote;
  font-size: 2rem;
  color: var(--accent-blue);
}

.testimonial-author strong {
  color: var(--primary-dark);
  display: block;
  margin-bottom: 0.25rem;
  font-size: 1.1rem;
}

.testimonial-author span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Responsive Design for Portfolio */
@media (max-width: 768px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .portfolio-filter {
    gap: 0.5rem;
  }

  .portfolio-filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .project-results {
    gap: 1rem;
  }

  .result-number {
    font-size: 1.5rem;
  }

  .process-steps-grid {
    grid-template-columns: 1fr;
  }

  .testimonial-slide blockquote {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .portfolio-item {
    margin: 0 1rem;
  }

  .portfolio-info {
    padding: 1.5rem;
  }

  .project-results {
    flex-direction: column;
    gap: 1rem;
  }

  .overlay-content {
    padding: 1rem;
  }

  .overlay-content h3 {
    font-size: 1.2rem;
  }
}

/* Contact Page Styles */
.contact-section {
  padding: 3rem 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Contact Form */
.contact-form-container h2 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.contact-form-container p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.contact-form {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--shadow-light);
  border: 1px solid var(--border-light);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--pure-white);
  color: var(--secondary-gray);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Checkbox Group */
.checkbox-group {
  margin: 2rem 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  color: var(--text-light);
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-blue);
  border-color: var(--accent-blue);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  color: var(--pure-white);
  font-size: 0.8rem;
  font-weight: bold;
}

/* Contact Information */
.contact-info-container h2 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.contact-info-container p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 2px 10px var(--shadow-light);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.contact-method:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px var(--shadow-medium);
}

.method-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.method-content h3 {
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
  font-size: 1.1rem;
}

.method-content p {
  color: var(--secondary-gray);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.method-content span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Social Contact */
.social-contact {
  padding: 2rem;
  background: var(--soft-white);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.social-contact h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.social-contact .social-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-link {
  color: var(--text-light);
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.social-link:hover {
  background: var(--accent-blue);
  color: var(--pure-white);
  transform: translateX(5px);
}

/* FAQ Section */
.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--pure-white);
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px var(--shadow-light);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.faq-question {
  padding: 1.5rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: var(--soft-white);
}

.faq-question h3 {
  margin: 0;
  color: var(--primary-dark);
  font-size: 1.1rem;
}

.faq-toggle {
  font-size: 1.5rem;
  color: var(--accent-blue);
  font-weight: bold;
  transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 2rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 0 2rem 1.5rem;
  max-height: 200px;
}

.faq-answer p {
  color: var(--text-light);
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design for Contact Page */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-form {
    padding: 2rem;
  }

  .contact-method {
    padding: 1rem;
  }

  .method-icon {
    font-size: 1.5rem;
  }

  .social-contact {
    padding: 1.5rem;
  }

  .faq-question {
    padding: 1rem 1.5rem;
  }

  .faq-item.active .faq-answer {
    padding: 0 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .contact-form {
    padding: 1.5rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.75rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .method-content h3 {
    margin-bottom: 0.25rem;
  }
}
